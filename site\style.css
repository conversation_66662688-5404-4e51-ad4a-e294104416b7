/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 500;
    margin-bottom: 1rem;
    color: #2c3e50;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    color: #555;
}

a {
    color: #C62828;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #8B0000;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    line-height: 1.5;
}

.btn-primary {
    background-color: #C62828;
    color: white;
    box-shadow: 0 4px 12px rgba(198, 40, 40, 0.3);
}

.btn-primary:hover {
    background-color: #8B0000;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(198, 40, 40, 0.4);
    color: white;
}

.btn-secondary {
    background-color: transparent;
    color: #C62828;
    border: 2px solid #C62828;
}

.btn-secondary:hover {
    background-color: #C62828;
    color: white;
}

.btn-outline {
    background-color: transparent;
    color: #333;
    border: 2px solid #ddd;
}

.btn-outline:hover {
    background-color: #f8f9fa;
    border-color: #C62828;
    color: #C62828;
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
    color: white;
}

/* Header */
.header {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo h1 {
    color: #C62828;
    font-size: 1.8rem;
    margin-bottom: 0;
}

.logo span {
    color: #666;
    font-size: 0.9rem;
    font-weight: 300;
}

/* Navigation */
.nav-toggle {
    display: none;
}

.nav-toggle-label {
    display: none;
    flex-direction: column;
    cursor: pointer;
    width: 30px;
    height: 30px;
    justify-content: center;
}

.nav-toggle-label span,
.nav-toggle-label span::before,
.nav-toggle-label span::after {
    display: block;
    background: #333;
    height: 3px;
    width: 100%;
    border-radius: 3px;
    position: relative;
    transition: all 0.3s ease;
}

.nav-toggle-label span::before,
.nav-toggle-label span::after {
    content: '';
    position: absolute;
}

.nav-toggle-label span::before {
    top: -8px;
}

.nav-toggle-label span::after {
    top: 8px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #333;
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
    transition: color 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #C62828;
}

.nav-menu a.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #C62828;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 4rem 0;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.hero-text h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.hero-text p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: #666;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
}

/* Services Preview */
.services-preview {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.services-preview h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-size: 2.2rem;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.service-card h3 {
    color: #C62828;
    margin-bottom: 1rem;
}

.text-center {
    text-align: center;
}

/* Why Choose Us */
.why-choose {
    padding: 4rem 0;
}

.why-choose-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.features-list {
    list-style: none;
    margin-bottom: 2rem;
}

.features-list li {
    padding: 0.5rem 0;
    font-size: 1.1rem;
    color: #555;
}

.why-choose-image img {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
}

/* Contact Info */
.contact-info {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.contact-info h2 {
    text-align: center;
    margin-bottom: 3rem;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.contact-item {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.contact-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.contact-item h3 {
    color: #C62828;
    margin-bottom: 1rem;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #C62828 0%, #8B0000 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.page-header h1 {
    color: white;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
}

/* Footer */
.footer {
    background-color: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: #C62828;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bbb;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #C62828;
}

.footer-bottom {
    border-top: 1px solid #444;
    padding-top: 1rem;
    text-align: center;
    color: #bbb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-toggle-label {
        display: flex;
    }
    
    .nav-menu {
        position: absolute;
        top: 100%;
        left: 0;
        background: white;
        width: 100%;
        flex-direction: column;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        padding: 1rem 0;
    }
    
    .nav-toggle:checked ~ .nav-menu {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-toggle:checked + .nav-toggle-label span {
        background: transparent;
    }
    
    .nav-toggle:checked + .nav-toggle-label span::before {
        transform: rotate(45deg);
        top: 0;
    }
    
    .nav-toggle:checked + .nav-toggle-label span::after {
        transform: rotate(-45deg);
        top: 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-text h2 {
        font-size: 2rem;
    }
    
    .why-choose-content {
        grid-template-columns: 1fr;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-text h2 {
        font-size: 1.8rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }
}

/* About Page Styles */
.doctor-profile {
    padding: 4rem 0;
}

.profile-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    align-items: start;
}

.profile-image img {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
}

.profile-info h2 {
    color: #C62828;
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
}

.profile-info h3 {
    color: #666;
    font-weight: 400;
    margin-bottom: 1.5rem;
}

.intro {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    color: #555;
}

.specialties h4 {
    color: #C62828;
    margin-bottom: 1rem;
}

.specialties ul {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
}

.specialties li {
    padding: 0.5rem 0;
    color: #555;
    position: relative;
    padding-left: 1.5rem;
}

.specialties li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #C62828;
    font-weight: bold;
}

.education-experience {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #C62828;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #C62828;
}

.year {
    background-color: #C62828;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 0.5rem;
}

.timeline-item h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.timeline-item p {
    color: #666;
    margin-bottom: 0;
}

.philosophy {
    padding: 4rem 0;
}

.philosophy-content h2 {
    text-align: center;
    margin-bottom: 3rem;
}

.philosophy-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.philosophy-item {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.philosophy-item:hover {
    transform: translateY(-5px);
}

.philosophy-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.philosophy-item h3 {
    color: #C62828;
    margin-bottom: 1rem;
}

.memberships {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.memberships h2 {
    text-align: center;
    margin-bottom: 3rem;
}

.memberships-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.membership-item {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.membership-item h3 {
    color: #C62828;
    margin-bottom: 1rem;
}

/* Services Page Styles */
.services-detailed {
    padding: 4rem 0;
}

.services-grid-detailed {
    display: grid;
    gap: 3rem;
}

.service-detailed {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-detailed:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.service-header {
    background: linear-gradient(135deg, #C62828 0%, #8B0000 100%);
    color: white;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.service-icon-large {
    font-size: 3rem;
}

.service-header h2 {
    color: white;
    margin-bottom: 0;
}

.service-content {
    padding: 2rem;
}

.service-content h3 {
    color: #C62828;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.service-content ul {
    margin-bottom: 1.5rem;
}

.service-content li {
    margin-bottom: 0.5rem;
    color: #555;
}

.service-info {
    display: flex;
    gap: 2rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
    flex-wrap: wrap;
}

.duration, .price {
    background-color: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    color: #666;
}

.emergency-info {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.emergency-card {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 3rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 8px 30px rgba(220, 53, 69, 0.3);
}

.emergency-card h2 {
    color: white;
    margin-bottom: 1rem;
}

.emergency-card p {
    color: rgba(255,255,255,0.9);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.emergency-contacts {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.emergency-btn {
    background-color: white;
    color: #dc3545;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.emergency-btn:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    color: #dc3545;
}

.cta-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #C62828 0%, #8B0000 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    color: white;
    margin-bottom: 1rem;
}

.cta-content p {
    color: rgba(255,255,255,0.9);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

/* Appointment Page Styles */
.appointment-section {
    padding: 4rem 0;
}

.appointment-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
}

.appointment-info {
    background-color: #f8f9fa;
    padding: 2rem;
    border-radius: 12px;
    height: fit-content;
}

.info-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.info-card h3 {
    color: #C62828;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.info-card ul {
    list-style: none;
}

.info-card li {
    padding: 0.25rem 0;
    color: #555;
}

.appointment-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #C62828;
    box-shadow: 0 0 0 3px rgba(198, 40, 40, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.95rem;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: #C62828;
    border-color: #C62828;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.form-note {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #C62828;
}

.form-note p {
    margin-bottom: 0.5rem;
    color: #666;
    font-size: 0.95rem;
}

.alternative-contact {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.alternative-contact h2 {
    text-align: center;
    margin-bottom: 3rem;
}

.contact-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.contact-method {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.contact-method:hover {
    transform: translateY(-5px);
}

.contact-method .contact-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.contact-method h3 {
    color: #C62828;
    margin-bottom: 1rem;
}

/* Contact Page Styles */
.contact-details {
    padding: 4rem 0;
}

.contact-grid-main {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.contact-item-large {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.contact-icon-large {
    font-size: 2.5rem;
    flex-shrink: 0;
}

.contact-content h3 {
    color: #C62828;
    margin-bottom: 0.5rem;
}

.phone-link, .whatsapp-link {
    font-size: 1.2rem;
    font-weight: 500;
    color: #C62828;
}

.hours-access {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.hours-card, .access-card, .emergency-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.hours-card h2, .access-card h2 {
    color: #C62828;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.hours-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.hour-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
}

.hour-item.closed .time {
    color: #999;
}

.day {
    font-weight: 500;
    color: #333;
}

.time {
    color: #666;
}

.hours-note {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
}

.access-list {
    list-style: none;
}

.access-list li {
    padding: 0.5rem 0;
    color: #555;
}

.emergency-card {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.emergency-card h2 {
    color: white;
}

.emergency-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.map-section {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.map-section h2 {
    text-align: center;
    margin-bottom: 2rem;
}

.map-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.map-info {
    text-align: center;
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.map-info p {
    margin-bottom: 0.5rem;
    color: #666;
}

.contact-form-section {
    padding: 4rem 0;
}

.contact-form-content {
    max-width: 800px;
    margin: 0 auto;
}

.form-intro {
    text-align: center;
    margin-bottom: 3rem;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.quick-actions {
    padding: 4rem 0;
    background-color: #f8f9fa;
}

.quick-actions h2 {
    text-align: center;
    margin-bottom: 3rem;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.action-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    color: inherit;
}

.action-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.action-card h3 {
    color: #C62828;
    margin-bottom: 1rem;
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .profile-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .philosophy-grid {
        grid-template-columns: 1fr;
    }

    .memberships-grid {
        grid-template-columns: 1fr;
    }

    .service-header {
        flex-direction: column;
        text-align: center;
    }

    .service-info {
        flex-direction: column;
        gap: 1rem;
    }

    .emergency-contacts {
        flex-direction: column;
        align-items: center;
    }

    .emergency-btn {
        width: 100%;
        max-width: 300px;
    }

    .appointment-content {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .contact-methods {
        grid-template-columns: 1fr;
    }

    .contact-grid-main {
        grid-template-columns: 1fr;
    }

    .hours-access {
        order: -1;
    }

    .emergency-buttons {
        flex-direction: column;
        align-items: center;
    }

    .actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 480px) {
    .page-header h1 {
        font-size: 2rem;
    }

    .page-header p {
        font-size: 1rem;
    }

    .service-header {
        padding: 1.5rem;
    }

    .service-content {
        padding: 1.5rem;
    }

    .appointment-form {
        padding: 1.5rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .contact-item-large {
        flex-direction: column;
        text-align: center;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
a:focus,
button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid #C62828;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-primary {
        border: 2px solid #000;
    }

    .service-card,
    .contact-item,
    .philosophy-item {
        border: 1px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Print styles */
@media print {
    .header,
    .footer,
    .nav,
    .btn,
    .emergency-card,
    .cta-section {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
    }

    .container {
        max-width: none;
        padding: 0;
    }

    .page-header {
        background: none !important;
        color: #000 !important;
        padding: 1rem 0;
    }

    .page-header h1 {
        color: #000 !important;
    }
}
